using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using FasterGameLoading;
using HarmonyLib;
using MonoMod.Utils.Cil;
using System.Runtime.CompilerServices;
using System.Diagnostics;
using MonoMod.Utils;

public static class HarmonyPerformancePatches
{
    public static readonly ConditionalWeakTable<MethodBase, ParameterInfo[]> _parametersCache =
        new ConditionalWeakTable<MethodBase, ParameterInfo[]>();

    public static readonly ConditionalWeakTable<MethodInfo, IEnumerable<(ParameterInfo info, string realName)>> _originalParametersCache =
        new ConditionalWeakTable<MethodInfo, IEnumerable<(ParameterInfo info, string realName)>>();

    public static readonly ConditionalWeakTable<MemberInfo, IEnumerable<HarmonyArgument>> _argumentAttributesCache =
        new ConditionalWeakTable<MemberInfo, IEnumerable<HarmonyArgument>>();

    public static readonly ConditionalWeakTable<ParameterInfo, HarmonyArgument> _parameterArgumentCache =
        new ConditionalWeakTable<ParameterInfo, HarmonyArgument>();

    internal static readonly Dictionary<string, MethodInfo> _generatedMethodCache = new();
    internal static readonly Dictionary<string, Dictionary<int, CodeInstruction>> _finalInstructionsCache = new();

    private static string GetMethodPatcherKey(MethodPatcher patcher)
    {
        var originalKey = Utils.GetMethodKey(patcher.original);
        var prefixKeys = string.Join(",", patcher.prefixes.Select(Utils.GetMethodKey));
        var postfixKeys = string.Join(",", patcher.postfixes.Select(Utils.GetMethodKey));
        var finalizerKeys = string.Join(",", patcher.finalizers.Select(Utils.GetMethodKey));
        var transpilerKeys = string.Join(",", patcher.transpilers.Select(Utils.GetMethodKey));
        return $"{originalKey}|{prefixKeys}|{postfixKeys}|{finalizerKeys}|{transpilerKeys}";
    }

    private static CreateReplacementCacheEntry CreateCacheEntry(Dictionary<int, CodeInstruction> finalInstructions)
    {
        var entry = new CreateReplacementCacheEntry();
        var ilBytes = new List<byte>();
        var instructionData = new Dictionary<int, string>();

        foreach (var kvp in finalInstructions)
        {
            var instruction = kvp.Value;
            ilBytes.AddRange(BitConverter.GetBytes(kvp.Key));
            ilBytes.AddRange(BitConverter.GetBytes((int)instruction.opcode.Value));
            instructionData[kvp.Key] = instruction.operand?.ToString() ?? "";
        }

        entry.ilBytes = Convert.ToBase64String(ilBytes.ToArray());
        entry.instructionData = instructionData;
        return entry;
    }

    private static bool TryRestoreFromCache(string cacheKey, out MethodInfo method, out Dictionary<int, CodeInstruction> finalInstructions)
    {
        method = null;
        finalInstructions = null;

        if (_finalInstructionsCache.TryGetValue(cacheKey, out finalInstructions) && finalInstructions != null)
        {
            return true;
        }

        return false;
    }

    private static IEnumerable<HarmonyArgument> GetArgumentAttributesCached(this MemberInfo member)
    {
        return _argumentAttributesCache.GetValue(member, m =>
        {
            var attributes = m.GetCustomAttributes(false).OfType<HarmonyArgument>().ToList();
            if (m is MethodBase method)
            {
                var memberKey = Utils.GetMethodKey(method);
                FasterGameLoadingSettings.argumentAttributesCache[memberKey] = new HarmonyArgumentContainerList { list = attributes.Select(a => new HarmonyArgumentContainer(a)).ToList() };
            }
            return attributes;
        });
    }

    private static HarmonyArgument GetArgumentAttributeCached(this ParameterInfo parameter)
    {
        return _parameterArgumentCache.GetValue(parameter, p =>
        {
            var attribute = p.GetArgumentAttribute();
            if (attribute != null)
            {
                if (p.Member is MethodBase method)
                {
                    var parameterKey = $"{Utils.GetMethodKey(method)}-{p.Name}";
                    FasterGameLoadingSettings.parameterArgumentCache[parameterKey] = new HarmonyArgumentContainer(attribute);
                }
            }
            return attribute;
        });
    }

    [HarmonyPatch(typeof(MethodPatcher), nameof(MethodPatcher.OriginalParameters))]
    public static class OriginalParameters_Patch_V2
    {
        public static bool Prepare() => FasterGameLoadingSettings.useV2HarmonyPatches;
        [HarmonyPrefix]
        public static bool Prefix(MethodInfo method, ref IEnumerable<(ParameterInfo info, string realName)> __result)
        {
            var watch = Stopwatch.StartNew();
            LoadingActions.OriginalParametersPatchTracker.callCount++;
            if (_originalParametersCache.TryGetValue(method, out __result))
            {
                watch.Stop();
                LoadingActions.OriginalParametersPatchTracker.totalTicks += watch.ElapsedTicks;
                return false;
            }

            var methodKey = Utils.GetMethodKey(method);
            var baseArgs = method.GetArgumentAttributesCached();
            if (method.DeclaringType != null)
            {
                baseArgs = baseArgs.Concat(method.DeclaringType.GetArgumentAttributesCached());
            }

            var listResult = method.GetParameters().Select(delegate (ParameterInfo p)
            {
                var argumentAttribute = p.GetArgumentAttributeCached();
                var name = (argumentAttribute != null) ? (argumentAttribute.OriginalName ?? p.Name) : (baseArgs.GetRealName(p.Name, null) ?? p.Name);
                return (info: p, realName: name);
            }).ToList();

            _originalParametersCache.Add(method, listResult);

            var containerList = listResult.Select(item => new OriginalParameterContainer { parameterInfo = new ParameterInfoContainer(item.info), realName = item.realName }).ToList();
            FasterGameLoadingSettings.originalParametersCache[methodKey] = new OriginalParameterContainerList { list = containerList };

            __result = listResult;
            watch.Stop();
            LoadingActions.OriginalParametersPatchTracker.totalTicks += watch.ElapsedTicks;
            return false;
        }
    }

    [HarmonyPatch(typeof(MethodPatcher), "CreateDynamicMethod")]
    public static class CreateDynamicMethod_Patch_V2
    {
        public static bool Prepare() => FasterGameLoadingSettings.useV2HarmonyPatches;
        private static ParameterInfo[] GetCachedParameters(MethodBase method)
        {
            var watch = Stopwatch.StartNew();
            LoadingActions.CreateDynamicMethodPatchTracker.callCount++;
            var result = _parametersCache.GetValue(method, m =>
            {
                var parameters = m.GetParameters();
                var methodKey = Utils.GetMethodKey(m);
                FasterGameLoadingSettings.parametersCache[methodKey] = new ParameterInfoContainerList { list = parameters.Select(p => new ParameterInfoContainer(p)).ToList() };
                return parameters;
            });
            watch.Stop();
            LoadingActions.CreateDynamicMethodPatchTracker.totalTicks += watch.ElapsedTicks;
            return result;
        }

        [HarmonyTranspiler]
        public static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
        {
            var getParametersMethod = AccessTools.Method(typeof(MethodBase), "GetParameters");
            var getCachedParametersMethod = AccessTools.Method(typeof(CreateDynamicMethod_Patch_V2), nameof(GetCachedParameters));

            foreach (var instruction in instructions)
            {
                if (instruction.Calls(getParametersMethod))
                {
                    yield return new CodeInstruction(OpCodes.Call, getCachedParametersMethod);
                }
                else
                {
                    yield return instruction;
                }
            }
        }
    }

    [HarmonyPatch(typeof(MethodPatcher), "CreateReplacement")]
    public static class CreateReplacement_Patch_V2
    {
        public static bool Prepare() => FasterGameLoadingSettings.useV2HarmonyPatches;
        [HarmonyPrefix]
        public static bool Prefix(MethodPatcher __instance, out Dictionary<int, CodeInstruction> finalInstructions, ref MethodInfo __result)
        {
            var watch = Stopwatch.StartNew();
            var prefixes = __instance.prefixes;
            var postfixes = __instance.postfixes;
            var finalizers = __instance.finalizers;
            var transpilers = __instance.transpilers;
            var original = __instance.original;
            var source = __instance.source;
            var debug = __instance.debug;
            var il = __instance.il;
            var emitter = __instance.emitter;
            var returnType = __instance.returnType;
            var idx = __instance.idx;
            var patch = __instance.patch;

            LocalBuilder[] existingVariables = MethodPatcher.DeclareOriginalLocalVariables(il, source ?? original);
            var privateVars = new Dictionary<string, LocalBuilder>();

            var allFixes = new List<MethodInfo>(prefixes.Count + postfixes.Count + finalizers.Count);
            allFixes.AddRange(prefixes);
            allFixes.AddRange(postfixes);
            allFixes.AddRange(finalizers);

            var specialParamNames = new HashSet<string>();
            var needsState = false;
            var patchParamNames = new Dictionary<MethodInfo, List<(ParameterInfo info, string realName)>>();
            watch.Stop();
            LoadingActions.CreateReplacementInitSetupTracker.callCount++;
            LoadingActions.CreateReplacementInitSetupTracker.totalTicks += watch.ElapsedTicks;
            watch.Restart();
            foreach (var fix in allFixes)
            {
                var pNames = MethodPatcher.OriginalParameters(fix).ToList();
                patchParamNames[fix] = pNames;
                foreach (var p in pNames)
                {
                    if (p.realName.StartsWith("__", StringComparison.Ordinal))
                    {
                        specialParamNames.Add(p.realName);
                        if (p.realName == "__state") needsState = true;
                    }
                }
            }
            watch.Stop();
            LoadingActions.CreateReplacementAnalyzeParamsTracker.callCount++;
            LoadingActions.CreateReplacementAnalyzeParamsTracker.totalTicks += watch.ElapsedTicks;

            watch.Restart();
            LocalBuilder resultVar = null;
            if (idx > 0)
            {
                resultVar = __instance.DeclareLocalVariable(returnType, true);
                privateVars["__result"] = resultVar;
            }

            if (specialParamNames.Contains("__resultRef") && returnType.IsByRef)
            {
                var resultRefVar = il.DeclareLocal(typeof(RefResult<>).MakeGenericType(returnType.GetElementType()));
                emitter.Emit(OpCodes.Ldnull);
                emitter.Emit(OpCodes.Stloc, resultRefVar);
                privateVars["__resultRef"] = resultRefVar;
            }

            if (specialParamNames.Contains("__args"))
            {
                __instance.PrepareArgumentArray();
                var argsVar = il.DeclareLocal(typeof(object[]));
                emitter.Emit(OpCodes.Stloc, argsVar);
                privateVars["__args"] = argsVar;
            }

            Label? runOriginalLabel = null;
            LocalBuilder runOriginalVar = null;
            bool prefixAffectsOriginal = prefixes.Any(fix => MethodPatcher.PrefixAffectsOriginal(fix));
            bool needsRunOriginal = specialParamNames.Contains("__runOriginal");

            if (prefixAffectsOriginal || needsRunOriginal)
            {
                runOriginalVar = __instance.DeclareLocalVariable(typeof(bool), false);
                emitter.Emit(OpCodes.Ldc_I4_1);
                emitter.Emit(OpCodes.Stloc, runOriginalVar);
                if (prefixAffectsOriginal)
                    runOriginalLabel = il.DefineLabel();
            }

            if (needsState)
            {
                foreach (var fix in allFixes)
                {
                    if (fix.DeclaringType != null && !privateVars.ContainsKey(fix.DeclaringType.AssemblyQualifiedName))
                    {
                        var stateParam = patchParamNames[fix].FirstOrDefault(p => p.realName == "__state").info;
                        if (stateParam != null)
                        {
                            var stateVar = __instance.DeclareLocalVariable(stateParam.ParameterType, false);
                            privateVars[fix.DeclaringType.AssemblyQualifiedName] = stateVar;
                        }
                    }
                }
            }
            watch.Stop();
            LoadingActions.CreateReplacementPrepareLocalsTracker.callCount++;
            LoadingActions.CreateReplacementPrepareLocalsTracker.totalTicks += watch.ElapsedTicks;

            watch.Restart();
            LocalBuilder exceptionBlockTerminator = null;
            if (finalizers.Count > 0)
            {
                exceptionBlockTerminator = __instance.DeclareLocalVariable(typeof(bool), false);
                privateVars["__exception"] = __instance.DeclareLocalVariable(typeof(Exception), false);
                emitter.MarkBlockBefore(new ExceptionBlock(ExceptionBlockType.BeginExceptionBlock), out _);
            }
            watch.Stop();
            LoadingActions.CreateReplacementExceptionSetupTracker.callCount++;
            LoadingActions.CreateReplacementExceptionSetupTracker.totalTicks += watch.ElapsedTicks;

            watch.Restart();
            __instance.AddPrefixes(privateVars, runOriginalVar);
            watch.Stop();
            LoadingActions.CreateReplacementAddPrefixesTracker.callCount++;
            LoadingActions.CreateReplacementAddPrefixesTracker.totalTicks += watch.ElapsedTicks;

            if (runOriginalLabel.HasValue)
            {
                emitter.Emit(OpCodes.Ldloc, runOriginalVar);
                emitter.Emit(OpCodes.Brfalse, runOriginalLabel.Value);
            }

            watch.Restart();
            var copier = new MethodCopier(source ?? original, il, existingVariables);
            copier.SetDebugging(debug);
            transpilers.ForEach(copier.AddTranspiler);
            copier.AddTranspiler(PatchTools.m_GetExecutingAssemblyReplacementTranspiler);

            var endLabels = new List<Label>();
            copier.Finalize(emitter, endLabels, out var hasReturnCode, out var methodEndsInDeadCode);

            endLabels.ForEach(emitter.MarkLabel);

            if (resultVar != null && hasReturnCode)
                emitter.Emit(OpCodes.Stloc, resultVar);

            if (runOriginalLabel.HasValue)
                emitter.MarkLabel(runOriginalLabel.Value);
            watch.Stop();
            LoadingActions.CreateReplacementTranspileCopyTracker.callCount++;
            LoadingActions.CreateReplacementTranspileCopyTracker.totalTicks += watch.ElapsedTicks;

            watch.Restart();
            __instance.AddPostfixes(privateVars, runOriginalVar, false);

            if (resultVar != null && (hasReturnCode || (methodEndsInDeadCode && runOriginalLabel.HasValue)))
                emitter.Emit(OpCodes.Ldloc, resultVar);

            var hasPassthroughResult = __instance.AddPostfixes(privateVars, runOriginalVar, true);
            watch.Stop();
            LoadingActions.CreateReplacementPostfixesTracker.callCount++;
            LoadingActions.CreateReplacementPostfixesTracker.totalTicks += watch.ElapsedTicks;

            watch.Restart();
            if (finalizers.Count > 0)
            {
                if (hasPassthroughResult)
                {
                    emitter.Emit(OpCodes.Stloc, resultVar);
                    emitter.Emit(OpCodes.Ldloc, resultVar);
                }

                __instance.AddFinalizers(privateVars, runOriginalVar, false);

                emitter.Emit(OpCodes.Ldc_I4_1);
                emitter.Emit(OpCodes.Stloc, exceptionBlockTerminator);
                var rethrowLabel = il.DefineLabel();
                emitter.Emit(OpCodes.Ldloc, privateVars["__exception"]);
                emitter.Emit(OpCodes.Brfalse, rethrowLabel);
                emitter.Emit(OpCodes.Ldloc, privateVars["__exception"]);
                emitter.Emit(OpCodes.Throw);
                emitter.MarkLabel(rethrowLabel);

                emitter.MarkBlockBefore(new ExceptionBlock(ExceptionBlockType.BeginCatchBlock), out _);
                emitter.Emit(OpCodes.Stloc, privateVars["__exception"]);
                emitter.Emit(OpCodes.Ldloc, exceptionBlockTerminator);
                var finalizerEndLabel = il.DefineLabel();
                emitter.Emit(OpCodes.Brtrue, finalizerEndLabel);
                bool finalizersRethrow = __instance.AddFinalizers(privateVars, runOriginalVar, true);
                emitter.MarkLabel(finalizerEndLabel);

                var noExceptionLabel = il.DefineLabel();
                emitter.Emit(OpCodes.Ldloc, privateVars["__exception"]);
                emitter.Emit(OpCodes.Brfalse, noExceptionLabel);
                if (finalizersRethrow)
                    emitter.Emit(OpCodes.Rethrow);
                else
                {
                    emitter.Emit(OpCodes.Ldloc, privateVars["__exception"]);
                    emitter.Emit(OpCodes.Throw);
                }
                emitter.MarkLabel(noExceptionLabel);
                emitter.MarkBlockAfter(new ExceptionBlock(ExceptionBlockType.EndExceptionBlock));

                if (resultVar != null)
                    emitter.Emit(OpCodes.Ldloc, resultVar);
            }
            watch.Stop();
            LoadingActions.CreateReplacementFinalizersTracker.callCount++;
            LoadingActions.CreateReplacementFinalizersTracker.totalTicks += watch.ElapsedTicks;

            watch.Restart();
            if (!methodEndsInDeadCode || runOriginalLabel.HasValue || finalizers.Count > 0 || postfixes.Count > 0)
                emitter.Emit(OpCodes.Ret);

            finalInstructions = emitter.GetInstructions();
            if (debug)
            {
                FileLog.LogBuffered("DONE\n");
                FileLog.FlushBuffer();
            }
            watch.Stop();
            LoadingActions.CreateReplacementFinalizeReturnTracker.callCount++;
            LoadingActions.CreateReplacementFinalizeReturnTracker.totalTicks += watch.ElapsedTicks;

            watch.Restart();
            __result = patch.Generate();
            watch.Stop();
            LoadingActions.CreateReplacementGenerateTracker.callCount++;
            LoadingActions.CreateReplacementGenerateTracker.totalTicks += watch.ElapsedTicks;
            return false;
        }
    }

    [HarmonyPatch(typeof(MethodBodyReader), "FinalizeILCodes")]
    public static class FinalizeILCodes_Patch_V2
    {
        public static bool Prepare() => FasterGameLoadingSettings.useV2HarmonyPatches;
        private static void FastEmit(ILGenerator il, OpCode opcode, object operand)
        {
            var watch = Stopwatch.StartNew();
            LoadingActions.FinalizeILCodesPatchTracker.callCount++;
            switch (opcode.OperandType)
            {
                case OperandType.InlineNone:
                    il.Emit(opcode);
                    break;
                case OperandType.InlineString:
                    il.Emit(opcode, (string)operand);
                    break;
                case OperandType.InlineI:
                    il.Emit(opcode, (int)operand);
                    break;
                case OperandType.ShortInlineI:
                    if (opcode == OpCodes.Ldc_I4_S)
                        il.Emit(opcode, (sbyte)operand);
                    else
                        il.Emit(opcode, (byte)operand);
                    break;
                case OperandType.InlineMethod:
                    if (operand is ConstructorInfo ci) il.Emit(opcode, ci);
                    else il.Emit(opcode, (MethodInfo)operand);
                    break;
                case OperandType.InlineField:
                    il.Emit(opcode, (FieldInfo)operand);
                    break;
                case OperandType.InlineType:
                    il.Emit(opcode, (Type)operand);
                    break;
                case OperandType.InlineBrTarget:
                case OperandType.ShortInlineBrTarget:
                    il.Emit(opcode, (Label)operand);
                    break;
                case OperandType.InlineSwitch:
                    il.Emit(opcode, (Label[])operand);
                    break;
                case OperandType.InlineI8:
                    il.Emit(opcode, (long)operand);
                    break;
                case OperandType.InlineR:
                    il.Emit(opcode, (double)operand);
                    break;
                case OperandType.ShortInlineR:
                    il.Emit(opcode, (float)operand);
                    break;
                case OperandType.InlineVar:
                case OperandType.ShortInlineVar:
                    if (operand is LocalBuilder lb) il.Emit(opcode, lb);
                    else if (operand is LocalVariableInfo lvi) il.Emit(opcode, lvi.LocalIndex);
                    else il.Emit(opcode, (int)operand);
                    break;
                case OperandType.InlineTok:
                case OperandType.InlineSig:
                default:
                    il.DynEmit(opcode, operand);
                    break;
            }
            watch.Stop();
            LoadingActions.FinalizeILCodesPatchTracker.totalTicks += watch.ElapsedTicks;
        }

        [HarmonyTranspiler]
        public static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
        {
            var dynEmitMethod = AccessTools.Method(typeof(ILGeneratorShimExt), nameof(ILGeneratorShimExt.DynEmit), new Type[] { typeof(ILGenerator), typeof(OpCode), typeof(object) });
            var fastEmitMethod = AccessTools.Method(typeof(FinalizeILCodes_Patch_V2), nameof(FastEmit));

            foreach (var ins in instructions)
            {
                if (ins.Calls(dynEmitMethod))
                {
                    yield return new CodeInstruction(OpCodes.Call, fastEmitMethod);
                }
                else
                {
                    yield return ins;
                }
            }
        }
    }
}
